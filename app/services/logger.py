import logging
import logging.config
from copy import deepcopy
from toolbox.logger import LOGGING_CONFIG  # Import the base config

# Create extended configuration
EXTENDED_LOGGING_CONFIG = deepcopy(LOGGING_CONFIG)

# Add your custom formatters
EXTENDED_LOGGING_CONFIG["formatters"].update(
    {
        "base_formatter": {"format": "%(asctime)s - %(levelname)s - %(name)s - %(message)s"},
    }
)

# Add your custom handlers
EXTENDED_LOGGING_CONFIG["handlers"].update(
    {
        "base_handler": {"class": "logging.StreamHandler", "formatter": "base_formatter", "stream": "ext://sys.stderr"},
    }
)

# Add or modify loggers
EXTENDED_LOGGING_CONFIG["loggers"].update(
    {
        "kafka": {"level": "WARNING", "propagate": True},
        "kafka.conn": {"level": "WARNING"},
        "kafka.cluster": {"level": "WARNING"},
        "kafka.coordinator": {"level": "WARNING"},
        "kafka.coordinator.heartbeat": {"level": "WARNING"},
        "kafka.coordinator.consumer": {"level": "WARNING"},
        "kafka.consumer.subscription_state": {"level": "WARNING"},
        "kafka.consumer.fetcher": {"level": "WARNING"},
        "kafka.producer.kafka": {"level": "WARNING"},
    }
)

# Apply the extended configuration
logging.config.dictConfig(EXTENDED_LOGGING_CONFIG)
