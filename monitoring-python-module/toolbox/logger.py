import logging
import logging.config

# Define a custom facturation level between INFO (20) and WARNING (30)
FACTURATION_LEVEL = 25
logging.addLevelName(FACTURATION_LEVEL, "FACTURATION")


# -------------------------
# Custom Logging Filters
# -------------------------
class ExcludeEndpointFilter(logging.Filter):
    """
    Filters out log messages that contain certain endpoints (e.g. /health, /ready).
    """

    def filter(self, record):
        excluded_endpoints = ["/health", "/ready"]
        message = record.getMessage()
        return not any(endpoint in message for endpoint in excluded_endpoints)


# -------------------------
# Conditional Formatter
# -------------------------
class ConditionalFormatter(logging.Formatter):
    """
    Chooses which format to use based on the record.
    If record.is_facturation is True, uses the facturation format;
    otherwise, uses the standard format.
    """

    STANDARD_FORMAT = "%(asctime)s - %(levelname)s - %(name)s - %(job_id)s - %(message)s"
    FACTURATION_FORMAT = "%(asctime)s - %(levelname)s - %(name)s - %(job_id)s - " "%(cf1)s - %(cf2)s - %(cf3)s - %(decoded_token)s - %(message)s"

    def format(self, record):
        if getattr(record, "is_facturation", False):
            fmt = self.FACTURATION_FORMAT
        else:
            fmt = self.STANDARD_FORMAT
        # Set _style._fmt temporarily
        original_fmt = self._style._fmt
        self._style._fmt = fmt
        formatted = super().format(record)
        self._style._fmt = original_fmt  # restore original
        return formatted


# -------------------------
# Logging Configuration
# -------------------------
LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "filters": {
        "exclude_endpoints": {"()": ExcludeEndpointFilter},
    },
    "formatters": {
        "conditional": {
            "()": ConditionalFormatter,  # use our custom formatter class
        },
        "uvicorn_custom_formatter": {
            "format": "%(asctime)s - %(levelname)s - uvicorn - system - %(message)s",
        },
    },
    "handlers": {
        "default": {
            "class": "logging.StreamHandler",
            "formatter": "conditional",
            "stream": "ext://sys.stdout",
            "filters": ["exclude_endpoints"],
        },
        "uvicorn_custom_handler": {
            "class": "logging.StreamHandler",
            "formatter": "uvicorn_custom_formatter",
            "stream": "ext://sys.stdout",
            "filters": ["exclude_endpoints"],
        },
        "null_handler": {
            "class": "logging.NullHandler",
        },
    },
    "loggers": {
        "uvicorn": {
            "level": "INFO",
            "handlers": ["uvicorn_custom_handler"],
            "propagate": False,
        },
        "httpx": {
            "level": "INFO",
            "handlers": ["null_handler"],
            "propagate": False,
        },
        "": {  # Root logger configuration
            "level": "INFO",
            "handlers": ["default"],
            "propagate": True,
        },
    },
}

logging.config.dictConfig(LOGGING_CONFIG)


# -------------------------
# Custom Logger Adapter
# -------------------------
class ToolboxLoggerAdapter(logging.LoggerAdapter):
    """
    A LoggerAdapter that injects a default job_id and, for facturation logs,
    allows additional fields (cf1, cf2, cf3, decoded_token) to be logged.

    In standard logs, only job_id is passed.
    """

    def process(self, msg, kwargs):
        extra = kwargs.setdefault("extra", {})
        # Always include job_id (defaulting to "system")
        extra["job_id"] = extra.get("job_id", "system")
        # If this is NOT a facturation log, remove any facturation-specific fields.
        if not extra.get("is_facturation", False):
            extra.pop("cf1", None)
            extra.pop("cf2", None)
            extra.pop("cf3", None)
            extra.pop("decoded_token", None)
        return msg, kwargs

    def facturation(self, msg, decoded_token, cf1="", cf2="", cf3="", **kwargs):
        """
        Log a facturation event at the custom FACTURATION level.

        This method injects facturation-specific fields into the log record.
        """
        extra = kwargs.setdefault("extra", {})
        # Mark the record as a facturation log so the formatter can switch formats.
        extra["is_facturation"] = True
        extra["decoded_token"] = str(decoded_token)
        extra["cf1"] = cf1
        extra["cf2"] = cf2
        extra["cf3"] = cf3
        # Use the job_id provided in the decoded token if available.
        if isinstance(decoded_token, dict) and "job_id" in decoded_token:
            extra["job_id"] = decoded_token["job_id"]
        else:
            extra.setdefault("job_id", "system")
        self.log(FACTURATION_LEVEL, msg, **kwargs)


def get_logger(name=__name__):
    """
    Retrieve a logger adapter with standard defaults.

    For standard logs (info, debug, etc.), only job_id will be present.
    Facturation-specific fields are added only when using the facturation() method.
    """
    base_logger = logging.getLogger(name)
    return ToolboxLoggerAdapter(base_logger, {})
